'use client';
import React from 'react';
import { noop } from '@benzinga/utils';
import { StockSymbol } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import {
  GenericWidget,
  WidgetToolsManager,
  WidgetConfig,
  DispatchEvent,
  WidgetId,
  WidgetType,
} from '@benzinga/widget-tools';
import Hooks from '@benzinga/hooks';

export type SymbolWidgetType = string;
export type WorkspaceId = string;
export type WidgetsById = {
  [widgetId in WidgetId]: GenericWidget;
};
export type AddPopoutForWidget = { widgetId: WidgetId } | { widgetType: WidgetType; widgetConfig?: WidgetConfig };

export interface ProContextProps {
  addPopoutForWidget: (widgetArgs: AddPopoutForWidget) => void;
  addWidget: (
    widgetType: WidgetType,
    widgetConfig?: WidgetConfig,
    addToWorkspace?: WorkspaceId | 'current' | 'new' | 'none',
  ) => Promise<WidgetId | null>;
  addWidgetWithSymbol: (
    symbol: StockSymbol,
    widgetType: WidgetType,
    widgetConfig?: WidgetConfig,
    addToWorkspace?: WorkspaceId | 'current' | 'new' | 'none',
  ) => Promise<WidgetId | null>;
  dispatchEvent: (widgetId: WidgetId, event: DispatchEvent) => void;
  isPopout: boolean;
  getActiveWidgets: () => GenericWidget[];
  openHelpPage?: (helpPage: string) => void;
  removeWidget: (widgetId: WidgetId) => void;
  pauseWorkspacesDelete?: () => void;
  resumeWorkspacesDelete?: () => void;
  setDispatchEvent: (widgetId: WidgetId, event: (event: DispatchEvent) => void) => void;
  scheduleWorkspaceDelete?: (workspaceId: WorkspaceId) => void;
  cancelWorkspacesDelete?: () => void;
  getWorkspacesToBeDeleted?: () => WorkspaceId[];
  updateWidget?: (widgetId: WidgetId, widgetConfig: Partial<WidgetConfig>) => void;
}

export const ProContext = React.createContext<Required<ProContextProps>>({
  addPopoutForWidget: noop,
  addWidget: async () => null,
  addWidgetWithSymbol: async () => null,
  cancelWorkspacesDelete: noop,
  dispatchEvent: noop,
  getActiveWidgets: () => [],
  getWorkspacesToBeDeleted: () => [],
  isPopout: false,
  openHelpPage: noop,
  pauseWorkspacesDelete: noop,
  removeWidget: noop,
  resumeWorkspacesDelete: noop,
  scheduleWorkspaceDelete: noop,
  setDispatchEvent: noop,
  updateWidget: noop,
});

export type ProContextProviderProps = React.PropsWithChildren<
  Omit<ProContextProps, 'setDispatchEvent' | 'dispatchEvent' | 'addWidgetWithSymbol'>
>;
export const ProContextProvider: React.FC<ProContextProviderProps> = props => {
  const session = React.useContext(SessionContext);
  const [events] = React.useState(() => new Map<WidgetId, DispatchEvent[]>());
  const [eventMapping] = React.useState(() => new Map<WidgetId, (event: DispatchEvent) => void>());

  const dispatchEvent = React.useCallback(
    (widgetId: WidgetId, event: DispatchEvent) => {
      const eventHandler = eventMapping.get(widgetId);
      if (eventHandler) {
        eventHandler(event);
      } else {
        const eventHolder = events.get(widgetId);
        if (eventHolder) {
          eventHolder.push(event);
        } else {
          events.set(widgetId, [event]);
        }
      }
    },
    [eventMapping, events],
  );

  const setDispatchEvent = React.useCallback(
    (widgetId: WidgetId, event: (event: DispatchEvent) => void) => {
      eventMapping.set(widgetId, event);

      const eventHolder = events.get(widgetId);
      if (eventHolder) {
        eventHolder.forEach(event);
        events.delete(widgetId);
      }
    },
    [eventMapping, events],
  );

  const addWidgetWithSymbol = React.useCallback(
    async (
      symbol: StockSymbol,
      widgetType: string,
      widgetConfig?: WidgetConfig,
      addToWorkspace?: WorkspaceId | 'current' | 'new' | 'none',
    ) => {
      const addWidget = props.addWidget;
      const widgetId = await addWidget(widgetType, widgetConfig, addToWorkspace);
      if (!widgetId) return widgetId;
      dispatchEvent(widgetId, { symbol, type: 'symbol_update' });
      return widgetId;
    },
    [dispatchEvent, props.addWidget],
  );

  const updateWidget = React.useCallback(
    async (widgetId: WidgetId, widgetConfig: Partial<WidgetConfig>) => {
      if (props.updateWidget) {
        return props.updateWidget(widgetId, widgetConfig);
      }
      try {
        const widgetToolsManager = session.getManager(WidgetToolsManager);
        const currentParameters: Partial<WidgetConfig['parameters']> = widgetToolsManager.getWidgetParameters(widgetId);
        widgetToolsManager.updateManager({
          parameters: {
            ...currentParameters,
            ...widgetConfig.parameters,
          },
          type: 'parameterChangeList',
          widgetId,
        });

        return true;
      } catch (error) {
        console.error('Failed to update widget:', error);
        return false;
      }
    },
    [props, session],
  );

  Hooks.useSubscriber(session.getManager(WidgetToolsManager), event => {
    switch (event.type) {
      case 'widget-tools:event_to_widget':
        dispatchEvent(event.widgetId, event.event);
        break;
    }
  });

  const value = React.useMemo<Required<ProContextProps>>(
    () => ({
      addPopoutForWidget: props.addPopoutForWidget ?? noop,
      addWidget: props.addWidget ?? noop,
      addWidgetWithSymbol: addWidgetWithSymbol ?? noop,
      cancelWorkspacesDelete: props.cancelWorkspacesDelete ?? noop,
      dispatchEvent,
      getActiveWidgets: props.getActiveWidgets ?? (() => []),
      getWorkspacesToBeDeleted: props.getWorkspacesToBeDeleted ?? (() => []),
      isPopout: props.isPopout ?? false,
      openHelpPage: props.openHelpPage ?? noop,
      pauseWorkspacesDelete: props.pauseWorkspacesDelete ?? noop,
      removeWidget: props.removeWidget ?? noop,
      resumeWorkspacesDelete: props.resumeWorkspacesDelete ?? noop,
      scheduleWorkspaceDelete: props.scheduleWorkspaceDelete ?? noop,
      setDispatchEvent,
      updateWidget,
    }),
    [
      props.addPopoutForWidget,
      props.addWidget,
      props.cancelWorkspacesDelete,
      props.getActiveWidgets,
      props.getWorkspacesToBeDeleted,
      props.isPopout,
      props.openHelpPage,
      props.pauseWorkspacesDelete,
      props.removeWidget,
      props.resumeWorkspacesDelete,
      props.scheduleWorkspaceDelete,
      addWidgetWithSymbol,
      dispatchEvent,
      setDispatchEvent,
      updateWidget,
    ],
  );

  return <ProContext.Provider value={value}>{props.children}</ProContext.Provider>;
};
