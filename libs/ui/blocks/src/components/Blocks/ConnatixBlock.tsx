'use client';
import React from 'react';
import LazyLoad from 'react-lazyload';
import styled from '@benzinga/themetron';
import type { DeviceType } from '@benzinga/device-utils';
import { useBenzingaEdge } from '@benzinga/edge';

const ConnatixVideoPlayer = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixVideoPlayer };
  }),
);

const ConnatixInitialHeadScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixInitialHeadScript };
  }),
);

interface ConnatixVideoProps {
  attrs?: {
    data?: {
      id: string;
      client_id: string;
      player_id: string;
      device_type: 'all' | 'desktop' | 'mobile';
      channels?: string[];
    };
  };
  deviceType?: DeviceType;
}

export const ConnatixBlock: React.FC<ConnatixVideoProps> = ({ attrs, deviceType }) => {
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  if (hasAdLight) return null;

  if (!attrs?.data) return null;
  const { channels, client_id, id, player_id } = attrs.data;

  if (attrs?.data?.device_type === 'mobile' && deviceType !== 'mobile') return null;
  else if (attrs?.data?.device_type === 'desktop' && deviceType === 'mobile') return null;

  console.log('ConnatixBlock', attrs);
  return (
    <ConnatixVideoWrapper>
      <ConnatixInitialHeadScript id={id} />

      <LazyLoad offset={100} once style={{ minHeight: 170, width: '100%' }}>
        <ConnatixVideoPlayer channels={channels} cid={client_id} id={id} pid={player_id} />
      </LazyLoad>
    </ConnatixVideoWrapper>
  );
};

export const ConnatixVideoWrapper = styled.div`
  display: block;
  margin: 1rem 0;
  min-height: 170px;
  height: 170px;
  width: 100%;
  background-color: red;
`;
