'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import LazyLoad from 'react-lazyload';
import { useConnatixLivestream } from '@benzinga/livestream-hooks';
import Hooks from '@benzinga/hooks';
import { Block } from '../Block';

const ConnatixVideoPlayer = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixVideoPlayer };
  }),
);

const ConnatixInitialHeadScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixInitialHeadScript };
  }),
);

interface ConnatixVideoProps {
  attrs?: {
    data?: {
      non_live_widget: any;
    };
  };
}

export const ConnatixLiveBlock: React.FC<ConnatixVideoProps> = ({ attrs }) => {
  const { assembleLivestream, livestreamData } = useConnatixLivestream();

  Hooks.useEffectDidMount(() => {
    assembleLivestream();
  });

  if (!livestreamData && attrs?.data?.non_live_widget) {
    return <Block block={attrs?.data?.non_live_widget} />;
  } else if (!livestreamData) {
    return null;
  }

  console.log('ConnatixLiveBlock1', attrs);

  return (
    <ConnatixVideoWrapper>
      <ConnatixInitialHeadScript id={livestreamData.id} />

      <LazyLoad offset={100} once style={{ minHeight: 170, width: '100%' }}>
        <ConnatixVideoPlayer {...livestreamData} />
      </LazyLoad>
    </ConnatixVideoWrapper>
  );
};

export const ConnatixVideoWrapper = styled.div`
  display: block;
  margin: 1rem 0;
`;
