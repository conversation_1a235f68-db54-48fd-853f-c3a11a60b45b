import { DateTime } from 'luxon';
/* eslint-disable no-param-reassign */
import parseISO from 'date-fns/parseISO';
import formatDate from 'date-fns/format';
import isToday from 'date-fns/isToday';
import isFuture from 'date-fns/isFuture';
import isPast from 'date-fns/isPast';
import compareDesc from 'date-fns/compareDesc';
import compareAsc from 'date-fns/compareAsc';
import tippy from 'tippy.js';
import { IDateRange } from '../interfaces';
import { getSessionSingleton } from '@benzinga/session';
import { DelayedQuote, Logo, QuotesManager } from '@benzinga/quotes-manager';
import { abbreviateNumber, chunkifyArray } from '@benzinga/utils';
import { SafePromise, SafeType } from '@benzinga/safe-await';
import { FDA, Ratings } from '@benzinga/calendar-manager';
import { ColumnDef } from '@benzinga/table';
import { Ticker, TickerLink } from '@benzinga/ticker-ui';
import { InsiderNetWorthLink, SecFilingLink, InsiderTickerCikLink } from '../components/FrameworkComponents';

const gainCellClass = 'gain-cell';
const loseCellClass = 'lose-cell';

export const binaryEventsCatalysts = [
  'Adcom',
  'PDUFA Date',
  'NDA Filing',
  'Top-line Data Due',
  'Foreign Approval',
  'BLA Filing',
  'Efficacy Data Due',
  'FDA Approval',
  'Initial Data Due',
  'IND Filing',
  'Interim Data Due',
  'Interim Results',
  'Top-line Results',
  'sNDA Filing',
  'sNDA Resubmission',
  'sBLA Filing',
  'sBLA Withdrawal',
  'NDA Resubmission',
  'NDA Withdrawal',
  'CRL',
  'Primary Endpoint',
  'Announced Clearance to Study',
  'Designation Grant',
  'FDA Clearance',
  'FDA Meeting',
  'Received Use Authorization',
  'Regulatory Interaction',
  'Regulatory Update',
];

const dateFilterParams = {
  comparator(filterLocalDateAtMidnight: Date, cellValue: string) {
    const dateAsString = cellValue;

    if (dateAsString === null || dateAsString === undefined || dateAsString === '') {
      return 0;
    }

    const dateParts = dateAsString.split('-');
    const day = Number(dateParts[2]);
    const month = Number(dateParts[1]) - 1;
    const year = Number(dateParts[0]);
    const cellDate = new Date(year, month, day);

    if (cellDate < filterLocalDateAtMidnight) {
      return -1;
    }
    if (cellDate > filterLocalDateAtMidnight) {
      return 1;
    }
    return 0;
  },
};

const headerInfoTooltipDef = (info: string): { headerTooltip: string } => {
  return {
    headerTooltip: info,
  };
};

const initTableHint = (selector: string, content: string) => {
  return tippy(selector, {
    content: content,
    placement: 'top',
    showOnCreate: true,
    theme: 'light-border',
  });
};

function formatPrice(price: string | number, digits = 2, toLocale = false): string {
  if (!price) return '-';

  price = Number(Number(price).toFixed(digits));

  if (toLocale) {
    price = Number(price).toLocaleString('en');
  }

  return `$${price}`;
}

function formatNumber(number: string | number, additionalSymbol = ''): string {
  if (number === null || number === undefined || number === '') return '-';

  return Number(number).toFixed(2) + additionalSymbol;
}

function formatLongPrice(value: string | number, prefix = '$'): string {
  if (value === null || value === undefined || value === '') return '-';

  let mark = '';
  let number = Number(value);

  if (number >= 1000000000 || number <= -1000000000) {
    mark = 'B';
    number /= 1000000000;
  } else if (number >= 1000000 || number <= -1000000) {
    mark = 'M';
    number /= 1000000;
  } else if (number >= 1000 || number <= -1000) {
    mark = 'K';
    number /= 1000;
  }

  if (number >= 0) {
    return prefix + Number(number).toFixed(2) + mark;
  }
  return `-${prefix}${Number(number * -1).toFixed(2)}${mark}`;
}

function formatLongQuantity(value: string | number): string {
  if (value === null || value === undefined || value === '') return '-';

  let mark = '';
  let number = Number(value);

  if (number >= 1000000000 || number <= -1000000000) {
    mark = 'B';
    number /= 1000000000;
  } else if (number >= 1000000 || number <= -1000000) {
    mark = 'M';
    number /= 1000000;
  } else if (number >= 1000 || number <= -1000) {
    mark = 'K';
    number /= 1000;
  }

  if (number >= 0) {
    return Number(number).toFixed(2) + mark;
  }
  return `-${Number(number * -1).toFixed(2)}${mark}`;
}

function formatMillions(price: number, short = false): string {
  if (!price) return '-';

  if (short) {
    return price < 1000000000
      ? `$${Number(price / 1000000).toFixed(0)}M`
      : `$${Number(price / 1000000000).toFixed(1)}B`;
  }

  return `$${Number(price).toLocaleString('en')}`;
}

function getPureNumber(value: string | number): string | number {
  if (!value) return '';

  return Number(value);
}

function transformPercentageValue(value: string | number): number | null {
  if (!value) return null;

  return Number(value) * 100;
}

function formatColumnDate(date: string, format = 'MM/dd/yyyy'): string {
  if (!date) return '-';

  const parsedDate = parseISO(date);
  if (parsedDate.toString() !== 'Invalid Date') {
    return formatDate(parsedDate, format);
  }

  return '-';
}

function getCellStatusClassRules(ratingStatus: string): string {
  if (ratingStatus === 'Raises') {
    return gainCellClass;
  }
  if (ratingStatus === 'Lowers') {
    return loseCellClass;
  }
  return '';
}

function formatPriceChange(previous: string | number, current: string | number): string {
  if (previous && current) {
    return `${formatPrice(previous)} → ${formatPrice(current)}`;
  }
  if (current) {
    return ` → ${formatPrice(current)}`;
  }

  return '';
}

function formatRatingsChange(previous: string, current: string, ratingStatus: string): string {
  if (ratingStatus === 'Maintains') {
    return current;
  }

  return `${previous} → ${current}`;
}

interface SortedAnalystRatings {
  downgrades: Ratings[];
  initiations: Ratings[];
  upgrades: Ratings[];
}

function sortAnalystRatings(items: Ratings[]) {
  const result: SortedAnalystRatings = {
    downgrades: [],
    initiations: [],
    upgrades: [],
  };

  if (!Array.isArray(items) || items.length === 0) return result;

  const initiations: Ratings[] = [];
  const downgrades: Ratings[] = [];
  const upgrades: Ratings[] = [];

  items.forEach(item => {
    if (item.action_company === 'Initiates Coverage On') {
      initiations.push(item);
    } else if (item.action_company === 'Downgrades') {
      downgrades.push(item);
    } else if (item.action_company === 'Upgrades') {
      upgrades.push(item);
    }
  });

  result.initiations = initiations;
  result.downgrades = downgrades;
  result.upgrades = upgrades;

  return result;
}

interface SortedFDAData {
  estimated: FDA[];
  exact: FDA[];
  historical: FDA[];
  pdufa: FDA[];
}

function sortFDAData(items: FDA[], binaryEventsOnly?: boolean) {
  const result: SortedFDAData = {
    estimated: [],
    exact: [],
    historical: [],
    pdufa: [],
  };

  if (!Array.isArray(items) || items.length === 0) return result;

  const historical: FDA[] = [];
  const exact: FDA[] = [];
  const estimated: FDA[] = [];
  const pdufa: FDA[] = [];

  if (binaryEventsOnly) {
    items = filterBinaryEvents(items);
  }

  items.forEach(item => {
    try {
      const target_date = parseISO(item.target_date);
      if (target_date.toString() !== 'Invalid Date' && item.target_date.length > 7) {
        if (isToday(target_date) || isFuture(target_date)) {
          exact.push(item);
        } else {
          historical.push(item);
        }
      } else if (item?.target_date === '' || isPast(target_date)) {
        historical.push(item);
      } else if (item?.target_date?.length !== 0) {
        estimated.push(item);
      }

      if (item.event_type === 'Adcom Date' || item.event_type === 'PDUFA Date') {
        pdufa.push(item);
      }
    } catch (error: any) {
      console.error(error);
    }
  });

  historical.sort((a, b) => compareDesc(parseISO(a.target_date), parseISO(b.target_date)));
  exact.sort((a, b) => compareAsc(parseISO(a.target_date), parseISO(b.target_date)));

  result.historical = historical;
  result.exact = exact;
  result.estimated = estimated;
  result.pdufa = pdufa;

  return result;
}

function filterBinaryEvents(items: any) {
  if (!Array.isArray(items)) return [];

  return items.filter(item => binaryEventsCatalysts.includes(item.event_type));
}

function getCellStatusBackgroundByActionCompany(actionCompany: string): string {
  const gain = ['Upgrades', 'Initiates Coverage On', 'Reinstates', 'Reinstates'];
  const lose = ['Downgrades'];

  if (gain.includes(actionCompany)) {
    return gainCellClass;
  }
  if (lose.includes(actionCompany)) {
    return loseCellClass;
  }

  return '';
}

function formatActionCompany(actionCompany: string): string {
  if (actionCompany === 'Initiates Coverage On') {
    return 'Initiates';
  }

  if (actionCompany === 'Upgrades') {
    return 'Upgrade';
  }

  if (actionCompany === 'Downgrades') {
    return 'Downgrade';
  }

  return actionCompany;
}

function formatUpsideDownside(previous: string | number, current: string | number): string | number {
  if (!previous || !current) return '';

  previous = Number(previous);
  current = Number(current);

  const calculated = Number(((current - previous) / previous) * 100).toFixed(2);

  return Number(calculated);
}

function getValueBgClass(value: number | string): string {
  if (typeof value === 'string') {
    value = Number(value);
  }
  if (value < 0) {
    return loseCellClass;
  }
  if (value > 0) {
    return gainCellClass;
  }

  return '';
}

function dateValueGetter(value: any) {
  if (!value) return null;

  return value;
}

function formatPercentage(value: string | number, falsyReturn = '-', fixed = true) {
  if (!value) return falsyReturn;
  return fixed ? `${Number(value).toFixed(2)}%` : `${value}%`;
}

enum EPSType {
  Adj = 'Adj',
  FFO = 'FFO',
  GAAP = 'GAAP',
}

enum EconomicType {
  Barrels = 'Barrels',
}

enum CurrencyType {
  '$' = 'USD',
  '€' = 'EUR',
  '₹' = 'INR',
  '£' = 'GBP',
}

export const formatAbstractNumberToString = (
  value: number | string,
  formatType: CurrencyType | EPSType | EconomicType | '%' | string,
): string => {
  let valueNum = Number(value);
  const shouldBeFixed = value.toString().length < 5;
  if (shouldBeFixed) {
    valueNum = Number(Number(value).toFixed(2));
  }
  if (CurrencyType[formatType]) {
    const currency = CurrencyType[formatType];
    return abbreviateNumber(valueNum, { currency, style: 'currency' });
  } else if (EPSType[formatType] || EconomicType[formatType] || formatType === '') {
    return abbreviateNumber(valueNum, { currency: 'USD', style: 'currency' });
  } else if (formatType === '%') {
    return formatPercentage(valueNum, '—', shouldBeFixed);
  }
  return '—';
};

export const formatRequestDate = (date: Date) => {
  return formatDate(date, 'yyyy-MM-dd');
};

function updateLogos(logos: { [ticker: string]: string }, data: any[]) {
  if (Array.isArray(data)) {
    data.forEach(item => {
      if (item && item.ticker && logos) {
        if (logos[item.ticker]) {
          item.logo = logos[item.ticker];
        }
      }
    });
  }
  return data;
}

function updateQuotes(quotes: Record<string, DelayedQuote>, data: any[]) {
  if (Array.isArray(data)) {
    data.forEach(item => {
      if (item && item.ticker && quotes) {
        if (quotes[item.ticker]) {
          item.quote = quotes[item.ticker];
        }
      }
    });
  }
  return data;
}

function fetchTickersLogos(tickers: any[]) {
  const chunkifiedTickers: string[][] = chunkifyArray(tickers, 100);
  const promisesArray: SafePromise<Logo[] | SafeType<Logo[]>>[] = [];
  let result: Logo[] = [];

  const session = getSessionSingleton();
  const quoteManager = session.getManager(QuotesManager);

  chunkifiedTickers.forEach((tickerArray: string[]) => {
    const logosData = quoteManager.getQuotesLogos(tickerArray, {
      fields: 'mark_light',
    });
    promisesArray.push(logosData);
  });

  return Promise.all(promisesArray).then(arrayOfResults => {
    const allResults: Logo[] = [];
    arrayOfResults.forEach(({ ok }) => {
      if (Array.isArray(ok)) {
        allResults.push(...ok);
      }
    });
    result = allResults;
    const logos = {};
    if (result) {
      result.forEach((item: any) => {
        logos[item.search_key] = item.files['mark_light'];
      });
    }
    return logos;
  });
}

async function fetchTickersQuotes(tickers: string[]): Promise<Record<string, DelayedQuote>> {
  const chunkifiedTickers: string[][] = chunkifyArray(tickers, 100);
  const promisesArray: SafePromise<Record<string, DelayedQuote>>[] = [];

  const session = getSessionSingleton();
  const quoteManager = session.getManager(QuotesManager);

  chunkifiedTickers.forEach((tickerArray: string[]) => {
    const delayedQuotes = quoteManager.getDelayedQuotes(tickerArray);
    promisesArray.push(delayedQuotes);
  });

  return Promise.all(promisesArray).then(arrayOfResults => {
    let allResults: Record<string, DelayedQuote> = {};
    arrayOfResults.forEach(({ ok }) => {
      if (ok) {
        allResults = {
          ...allResults,
          ...ok,
        };
      }
    });
    return allResults;
  });
}

function injectLogos(items: any[]) {
  let tickers: string[] = items.map(item => item.ticker);
  tickers = Array.from(new Set(tickers));
  return fetchTickersLogos(tickers)
    .then(logos => updateLogos(logos, items))
    .catch(() => {
      return items;
    });
}

async function injectQuotes(items: any[], passedTickers?: string[]) {
  if (!Array.isArray(items) || !items.length) {
    return Promise.resolve([]);
  }

  let tickers: string[] = [];
  if (passedTickers && passedTickers.length) {
    tickers = passedTickers;
  } else {
    tickers = items.map(item => item.ticker);
    tickers = Array.from(new Set(tickers));
  }

  const tickerQuotes = await fetchTickersQuotes(tickers);

  const updatedQuotes: unknown[] = (tickerQuotes && updateQuotes(tickerQuotes, items)) || items;
  return Promise.resolve(updatedQuotes);
}

const formatIntervalQueryParams = (dateRange: any) => {
  return {
    'parameters[date_from]': dateRange?.date_from,
    'parameters[date_to]': dateRange?.date_to,
  };
};

const formatInterval = (dateFrom: DateTime, dateTo: DateTime): IDateRange => {
  return {
    date_from: dateFrom.toISODate(),
    date_to: dateTo.toISODate(),
  };
};

const formatIntervalLastOpenMarketDay = (dateFrom: DateTime, dateTo: DateTime): IDateRange => {
  return {
    date_from: getLastOpenMarketDay(dateFrom).dateRange.date_from,
    date_to: getLastOpenMarketDay(dateTo).dateRange.date_to,
  };
};

const getLastOpenMarketDay = (now: DateTime): { date: DateTime; dateRange: IDateRange } => {
  let value = now;
  if (now?.weekday === 6) {
    value = now.minus({ days: 1 });
  } else if (now?.weekday === 7) {
    value = now.minus({ days: 2 });
  }
  return {
    date: value,
    dateRange: formatInterval(value, value),
  };
};

const convertAgGridColumnDefsToTableColumnDefs = (columnDefs: any[]): ColumnDef[] => {
  const formattedColumnsDef: ColumnDef[] = (columnDefs ?? []).map(item => {
    const columnDef: ColumnDef = {
      ...item,
      cellRenderer: item?.cellRenderer,
      sortEnabled: true,
    };

    if (item?.width || item?.initialWidth) {
      columnDef.width = item.width || item.initialWidth;
    }

    if (['buy_now', 'get_alert'].includes(item.field)) {
      columnDef.shouldRender = true;
      columnDef.width = 90;
      columnDef.sortEnabled = false;
    }
    if (item.cellRenderer === 'tickerWidgetCell' && !columnDef.cellRenderer) {
      columnDef.cellRenderer = ({ data, value }: { data: any; value: any }) => {
        const key = columnDef.cellRendererParams?.tickerSymbolKey;
        const symbol = key ? data[key] : data.ticker;
        if (Array.isArray(value)) {
          const TickerMap = () => {
            return value.map((item: string) => (
              <Ticker
                key={item}
                symbol={item}
                targetElement={
                  <TickerLink className="inline truncate" symbol={item}>
                    {item}
                  </TickerLink>
                }
              />
            ));
          };
          return <TickerMap />;
        }
        return (
          <Ticker
            symbol={symbol}
            targetElement={
              <TickerLink className="inline truncate" symbol={symbol}>
                {value || symbol}
              </TickerLink>
            }
          />
        );
      };
    }
    if (item.cellRenderer === 'insiderTooltipCell') {
      columnDef.cellRenderer = ({
        value,
      }: {
        data: { any: any };
        value: { field: string; fieldVal: number; tooltipLines: string[] };
      }) => {
        return value.field ?? null;
      };
    }
    if (item.cellRenderer === 'secFilingLink') {
      columnDef.cellRenderer = ({ data }: { data: { any: any } }) => {
        return <SecFilingLink data={data} />;
      };
    }
    if (item.cellRenderer === 'insiderNetworthLink') {
      columnDef.cellRenderer = ({ data }: { data: { any: any } }) => {
        return <InsiderNetWorthLink data={data} />;
      };
    }
    if (item.cellRenderer === 'insiderTickerCikLink') {
      columnDef.cellRenderer = ({ data }: { data: { any: any } }) => {
        return <InsiderTickerCikLink data={data} />;
      };
    }
    return columnDef;
  });

  return formattedColumnsDef;
};

export {
  convertAgGridColumnDefsToTableColumnDefs,
  dateFilterParams,
  formatPrice,
  formatLongPrice,
  formatColumnDate,
  formatNumber,
  formatMillions,
  formatPriceChange,
  getCellStatusBackgroundByActionCompany,
  getCellStatusClassRules,
  formatUpsideDownside,
  formatRatingsChange,
  formatActionCompany,
  getValueBgClass,
  headerInfoTooltipDef,
  dateValueGetter,
  getPureNumber,
  formatPercentage,
  fetchTickersLogos,
  fetchTickersQuotes,
  updateLogos,
  updateQuotes,
  injectLogos,
  injectQuotes,
  initTableHint,
  sortAnalystRatings,
  sortFDAData,
  transformPercentageValue,
  formatIntervalQueryParams,
  formatInterval,
  formatIntervalLastOpenMarketDay,
  formatLongQuantity,
  getLastOpenMarketDay,
};
