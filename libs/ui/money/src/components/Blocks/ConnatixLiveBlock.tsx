'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import LazyLoad from 'react-lazyload';
import { useConnatixLivestream } from '@benzinga/livestream-hooks';
import Hooks from '@benzinga/hooks';
import { MoneyBlock } from '../MoneyBlock';

const ConnatixVideoPlayer = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixVideoPlayer };
  }),
);

const ConnatixInitialHeadScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixInitialHeadScript };
  }),
);

interface ConnatixVideoProps {
  attrs?: {
    data?: {
      non_live_widget: any;
    };
  };
  type: 'default' | 'sidebar';
}

export const ConnatixLiveBlock: React.FC<ConnatixVideoProps> = ({ attrs, type }) => {
  const { assembleLivestream, livestreamData } = useConnatixLivestream();

  Hooks.useEffectDidMount(() => {
    assembleLivestream();
  });

  if (!livestreamData && attrs?.data?.non_live_widget) {
    return <MoneyBlock block={attrs?.data?.non_live_widget} type={type} />;
  } else if (!livestreamData) {
    return null;
  }

  console.log('ConnatixLiveBlock2', attrs);

  return (
    <ConnatixVideoWrapper>
      <ConnatixInitialHeadScript id={livestreamData.id} />

      <LazyLoad offset={100} once style={{ minHeight: 170, width: '100%' }}>
        <ConnatixVideoPlayer {...livestreamData} />
      </LazyLoad>
    </ConnatixVideoWrapper>
  );
};

export const ConnatixVideoWrapper = styled.div`
  display: block;
  margin: 1rem 0;
`;
