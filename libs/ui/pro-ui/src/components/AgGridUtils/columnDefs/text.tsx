import styled from 'styled-components';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';
import { ICellRendererParams } from '@ag-grid-community/core';
import React from 'react';
import { ProContext } from '@benzinga/pro-tools';

const CellRenderer = (params: ICellRendererParams): React.ReactNode => {
  const field = params?.colDef?.field ?? '';
  const value = params.data[field];
  const proContext = React.useContext(ProContext);

  const widget = React.useMemo(
    () => proContext.getActiveWidgets().find(w => w.type === 'advancedNewsfeed'),
    [proContext],
  );
  const generateWidgetParameters = React.useCallback(
    (text: string) => ({
      parameters: {
        config: {
          feedExpression: {
            lhs: {
              expression: ['Title', 'phrase', text],
              operator: 'expression',
            },
            operator: 'or',
            rhs: {
              expression: ['Title', 'phrase', text],
              operator: 'expression',
            },
          },
          feedSettings: {
            //removing importance to show recent articles
            importance: {
              filters: [],
              id: 'off',
              title: text,
            },
            relevantCategories: {},
            //default sources
            sources: [
              'story',
              'pr_secfilings',
              'pr',
              'newswire_pr',
              'abnewswire',
              'accesswire_pr',
              'acnnewswire_story',
              'businesswire_story',
              'bz_pr_thomson_reuters',
              'comtex_story',
              'globenewswire_story',
              'kisspr',
              'marketwire_story',
              'newsdirect_pr',
              'newsfile_story',
              'newswire_pr',
              'newswire_story',
              'prweb_story',
              'pr_story',
              'thenewswire_pr',
              'webwire_story',
            ],
          },
        },
        flightMode: true,
      },
      widgetVersion: 12,
    }),
    [],
  );

  const handleArticleNodeClick = React.useCallback(
    (text: string) => {
      if (widget && widget.widgetId) {
        proContext.updateWidget(widget.widgetId, generateWidgetParameters(text));
        return;
      }
      proContext.addWidget('advancedNewsfeed', generateWidgetParameters(text), 'current');
    },
    [generateWidgetParameters, proContext, widget],
  );

  //handle article node links
  if (field?.toLowerCase().includes('withnodeid') && value) {
    const [, text] = value.split(':');
    return <StyledSpan onClick={() => handleArticleNodeClick(text)}>{text}</StyledSpan>;
  }

  //handle article title
  if (field.toLowerCase().includes('title') && value) {
    return value.split(':')[1];
  }

  //handle transaction type
  if (field === 'transaction_type') {
    return value.startsWith('S') ? value.replace('S', 'Sold') : 'Purchased';
  }

  return params.value ?? value;
};
export const TextColDef = (colDef: AdvancedColDef): AdvancedColDef => {
  return {
    cellStyle: { 'text-align': 'left' },
    filter: 'agTextColumnFilter',
    ...colDef,
    ...absColDef(colDef),
    cellRenderer: colDef.cellRenderer ?? CellRenderer,
  };
};

const StyledSpan = styled.span`
  color: ${props => props.theme.colors.brand};
  cursor: pointer;
`;
