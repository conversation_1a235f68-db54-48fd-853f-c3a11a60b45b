export enum LayoutChangeType {
  ADD = 'add',
  REMOVE = 'remove',
  REPLACE = 'replace',
}

export interface UpdateLayout<T = Layout> {
  replace?: Partial<T>;
  remove?: Partial<T>;
}

export interface Layout<T = any> {
  data: T;
  date_created?: string;
  date_updated?: string;
  ip_address: string;
  name: string;
  user_agent: string;
  uuid: string;
}

export interface LayoutListItem {
  uuid: string;
  date_created?: string;
  date_updated?: string;
  ip_address: string;
  is_favorite: boolean;
  layout_version: number;
  name: string;
  user_agent: string;
  widgets_count: number;
  workspaces_count: number;
}

export type LayoutList = LayoutListItem[];

export const LAYOUT_VERSION = 35;
