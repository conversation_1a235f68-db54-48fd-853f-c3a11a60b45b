import { SafeError, SafePromise } from '@benzinga/safe-await';
import { ExtendedListenableSubscribable } from '@benzinga/subscribable';
import { Layout, LayoutList } from './entities';
import { LayoutRestful } from './restful';
import { Operation } from 'fast-json-patch';
import { Session } from '@benzinga/session';

interface ErrorEvent {
  error?: SafeError;
  errorType: 'layouts_error' | 'layout_beacon_error' | 'post_layout_error' | 'put_layout_error' | 'delete_layout_error';
  type: 'error';
}

interface LayoutsListEvent {
  LayoutsList: LayoutList;
  type: 'layoutsList';
}

interface LayoutEvent {
  Layout: Layout;
  type: 'layout';
}

export type LayoutRequestEvent = ErrorEvent | LayoutsListEvent | LayoutEvent;

interface LayoutFunctions {
  getLayouts: LayoutRequest['getLayoutsList'];
}

export class LayoutRequest extends ExtendedListenableSubscribable<LayoutRequestEvent, LayoutFunctions> {
  private restful: LayoutRestful;

  constructor(session: Session) {
    super();
    this.restful = new LayoutRestful(session);
  }

  public getLayoutsList = async (): SafePromise<LayoutList> => {
    const layoutsList = await this.restful.getLayoutsList();
    if (layoutsList.err) {
      this.dispatch({
        error: layoutsList.err,
        errorType: 'layouts_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        LayoutsList: layoutsList.ok,
        type: 'layoutsList',
      });
    }
    return layoutsList;
  };

  public getLayout = async (uuid: string): SafePromise<Layout> => {
    const layout = await this.restful.getLayout(uuid);
    if (layout.err) {
      this.dispatch({
        error: layout.err,
        errorType: 'layouts_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        Layout: layout.ok,
        type: 'layout',
      });
    }
    return layout;
  };

  public beaconLayout = (uuid: Layout['uuid'], layout: Layout['data']): boolean => {
    const layoutError = this.restful.beaconLayout(uuid, layout);
    if (layoutError === false) {
      this.dispatch({
        errorType: 'layout_beacon_error',
        type: 'error',
      });
    }
    return layoutError;
  };

  public patchLayout = async (uuid: Layout['uuid'], operations: Operation[]): SafePromise<undefined> => {
    const layouts = await this.restful.patchLayout(uuid, operations);
    if (layouts.err) {
      this.dispatch({
        error: layouts.err,
        errorType: 'layouts_error',
        type: 'error',
      });
    }
    return layouts;
  };

  public postLayout = async (name?: string, layoutData?: Layout['data']): SafePromise<Layout> => {
    const response = await this.restful.postLayout(name, layoutData);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'post_layout_error',
        type: 'error',
      });
    }
    return response;
  };

  public putLayout = async (uuid: Layout['uuid'], layoutData: Layout['data']): SafePromise<Layout> => {
    const response = await this.restful.putLayout(uuid, layoutData);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'put_layout_error',
        type: 'error',
      });
    }
    return response;
  };

  public deleteLayout = async (uuid: Layout['uuid']): SafePromise<undefined> => {
    const layouts = await this.restful.deleteLayout(uuid);
    if (layouts.err) {
      this.dispatch({
        error: layouts.err,
        errorType: 'delete_layout_error',
        type: 'error',
      });
    }
    return layouts;
  };

  protected onSubscribe(): LayoutFunctions {
    return {
      getLayouts: this.getLayoutsList,
    };
  }
}
