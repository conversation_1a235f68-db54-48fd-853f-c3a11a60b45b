import { ListenableSubscribable } from '@benzinga/subscribable';
import { deepEqual } from '@benzinga/utils';
import { Layout, LayoutList, LayoutListItem } from './entities';

interface LayoutsListUpdateEvent {
  layoutsList: LayoutList | undefined;
  type: 'layouts:layouts_list_update' | 'layouts:layouts_list_item_update';
}

interface LayoutsListItemUpdateEvent {
  layoutsListItem: LayoutListItem | undefined;
  type: 'layouts:layouts_list_item_update';
}

export type LayoutStoreEvent = LayoutsListUpdateEvent | LayoutsListItemUpdateEvent;

export class LayoutStore extends ListenableSubscribable<LayoutStoreEvent> {
  private date?: Date;
  private layoutsList?: LayoutList;
  private cachedLayout: Layout | undefined;

  constructor() {
    super();
    this.date = undefined;
    this.layoutsList = undefined;
  }

  public static compareLayouts = (lhs: LayoutList, rhs: LayoutList): boolean => {
    return (
      lhs.length === rhs.length &&
      lhs?.every(layout => {
        const newLayout = rhs.find(newLayout => layout.uuid === newLayout.uuid);
        if (newLayout) {
          return deepEqual(layout, newLayout);
        } else {
          return false;
        }
      })
    );
  };

  public shouldWeFetchLayout = (): boolean => {
    const ONE_MIN = 60 * 1000; /* ms */
    const lastCalled = this.date?.getTime() ?? 0; // use this once all Layout are grabbed from manager
    if (this.layoutsList === undefined || Date.now() - lastCalled > ONE_MIN) {
      return true;
    }
    return false;
  };

  public getLayoutsList = (): LayoutList | undefined => {
    return this.layoutsList;
  };

  public getCachedLayout = (): Layout | undefined => this.cachedLayout;
  public updateCachedLayout = (layout: Layout): void => {
    this.cachedLayout = layout;
  };

  // returns true if no update was needed;
  public updateLayoutsList = (layoutsList: LayoutList) => {
    if (this.layoutsList && LayoutStore.compareLayouts(this.layoutsList, layoutsList)) {
      this.date = new Date();
    } else {
      this.layoutsList = layoutsList;
      this.dispatch({
        layoutsList: layoutsList,
        type: 'layouts:layouts_list_update',
      });
    }
  };

  public clearLayouts = () => {
    this.layoutsList = [];
    this.dispatch({
      layoutsList: undefined,
      type: 'layouts:layouts_list_update',
    });
  };

  public addLayout = (layout: LayoutListItem): void => {
    if (this.layoutsList) {
      this.layoutsList.push(layout);
    } else {
      this.layoutsList = [layout];
    }
  };

  public updateLayout = (layout: Layout['data']): void => {
    this.layoutsList = this.layoutsList?.map(item => (item.uuid === layout.uuid ? layout : item));

    this.dispatch({
      layoutsListItem: layout,
      type: 'layouts:layouts_list_item_update',
    });
  };

  public removeLayout = (layout: LayoutListItem): void => {
    this.layoutsList?.filter(item => item.uuid !== layout.uuid);
  };
}
