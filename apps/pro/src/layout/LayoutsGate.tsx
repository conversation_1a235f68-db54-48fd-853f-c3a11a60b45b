import { LocalStorage<PERSON><PERSON>, PersistStorage<PERSON><PERSON>, SessionStorageKey } from '@benzinga/fission';
import React from 'react';
import { connect } from 'react-redux';

import { LAYOUT_VERSION, newLayoutFromServer, sendPersistedLayoutToServer } from '../actions/layoutActions';
import { AllLayoutData, LayoutData } from '../entities/layoutEntity';
import { RootState } from '../redux/types';
import { selectActiveTheme, selectSettings } from '../selectors/settingsSelectors';
import { selectWidgets } from '../selectors/widgetSelectors';
import { selectWorkspaces } from '../components/dashboard/workspaceSelectors';
import { widgetsTransform, workspacesTransform } from './transformations';
import { selectInitialized } from '../selectors/appSelectors';
import { Widgets, WidgetsById } from '../entities/widgetEntity';
import { Workspaces } from '../components/dashboard/workspaceEntity';
import { selectAuth<PERSON>ey } from '../selectors/sessionSelectors';
import { SessionContext } from '@benzinga/session-context';
import { Layout, LayoutManager } from '@benzinga/layout-manager';
import { Session } from '@benzinga/session';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import Hooks from '@benzinga/hooks';
import { Settings } from '../entities/settingsEntity';

const PERSIST_TIMEOUT = 1000;
const FLUSH_TIMEOUT = 60000;

const mapStateToProps = (state: RootState) => ({
  authKey: selectAuthKey(state),
  initialized: selectInitialized(state),
  settings: selectSettings(state),
  theme: selectActiveTheme(state),
  widgetsById: selectWidgets(state),
  workspaces: selectWorkspaces(state),
});

type ReduxState = ReturnType<typeof mapStateToProps>;

type Props = ReduxState;

const saveServer = async (
  layoutLoaded: boolean,
  settings: Settings | null,
  widgetsTransformed: WidgetsById | null,
  workspacesTransformed: Workspaces | null,
  session: Session,
) => {
  if (!layoutLoaded || settings === null || widgetsTransformed === null || workspacesTransformed === null) {
    return;
  }
  const data: LayoutData = {
    layoutVersion: LAYOUT_VERSION,
    proHash: process.env.GIT_COMMITHASH ?? 'unknown',
    proVersion: process.env.RELEASE_VERSION ?? 'unknown',
    settings,
    widgets: (widgetsTransformed ? widgetsTransform(widgetsTransformed) : null) as Widgets,
    workspaces: workspacesTransformed as Workspaces,
  };
  const layoutId = window.sessionStorage.getItem(PersistStorageKey.activeLayoutId);
  let savedLayout: Layout<AllLayoutData> | undefined;
  if (layoutId) {
    const requestLayout = {
      data,
      uuid: layoutId,
    } as Layout<AllLayoutData>;
    savedLayout = await sendPersistedLayoutToServer(requestLayout, session);
  } else {
    savedLayout = await newLayoutFromServer(undefined, data);
  }
  savedLayout && window.sessionStorage.setItem(PersistStorageKey.activeLayoutId, savedLayout.uuid);
};

const useSaveSessionStorage = <T extends unknown>(
  location: PersistStorageKey,
  config: T,
  layoutLoaded: boolean,
  toSave?: (config: NonNullable<T>) => string,
) => {
  const saveSessionStorage = React.useCallback(
    (location: PersistStorageKey, config: NonNullable<T>, toSave?: (config: NonNullable<T>) => string) =>
      window.sessionStorage.setItem(location, toSave ? toSave(config) : JSON.stringify(config)),
    [],
  );
  const PersistStorageKeySave = Hooks.useThrottleLeading(saveSessionStorage, PERSIST_TIMEOUT);
  const PrevConfig = React.useRef<T | undefined>(undefined);
  React.useEffect(() => {
    if (layoutLoaded && config && PrevConfig.current !== config) {
      PersistStorageKeySave(location, config, toSave);
      PrevConfig.current = config;
    }
  }, [PersistStorageKeySave, PrevConfig, config, layoutLoaded, location, toSave]);

  React.useEffect(() => {
    const handle = () => {
      if (layoutLoaded && config && PrevConfig.current && PrevConfig.current !== config) {
        saveSessionStorage(location, config, toSave);
      }
    };
    window.addEventListener('beforeunload', handle);
    return () => window.removeEventListener('beforeunload', handle);
  }, [PersistStorageKeySave, PrevConfig, config, layoutLoaded, location, saveSessionStorage, toSave]);
};

const LayoutsGate: React.FC<React.PropsWithChildren<Props>> = props => {
  const { initialized, settings, theme, widgetsById, workspaces } = props;
  const session = React.useContext(SessionContext);
  const isLoggedIn = useIsUserLoggedIn();
  const layoutLoaded =
    session.getManager(LayoutManager).getStoredLayoutsList() !== undefined && isLoggedIn && initialized;

  const widgetsTransformed = React.useMemo(() => (initialized ? widgetsById : null), [widgetsById, initialized]);
  useSaveSessionStorage(
    PersistStorageKey.widgets,
    widgetsTransformed,
    layoutLoaded,
    React.useCallback((config: WidgetsById) => JSON.stringify(widgetsTransform(config)), []),
  );

  const workspacesTransformed = React.useMemo(
    () => (initialized ? workspacesTransform(workspaces) : null),
    [workspaces, initialized],
  );
  useSaveSessionStorage(PersistStorageKey.workspaces, workspacesTransformed, layoutLoaded);

  const settingTransformed = React.useMemo(() => (initialized ? settings : null), [settings, initialized]);
  useSaveSessionStorage(PersistStorageKey.settings, settingTransformed, layoutLoaded);

  useSaveSessionStorage(PersistStorageKey.layoutVersion, `${LAYOUT_VERSION}`, layoutLoaded);

  React.useEffect(() => {
    if (layoutLoaded) {
      const popoutData = JSON.parse(window.localStorage.getItem(LocalStorageKey.popoutDataTransfer) || '{}');
      if (popoutData && Object.keys(popoutData).length) {
        Object.keys(popoutData).forEach(key => {
          if (key in popoutData && key in widgetsById) popoutData[key] = widgetsById[key]?.parameters;
        });
        window.localStorage.setItem(LocalStorageKey.popoutDataTransfer, JSON.stringify(popoutData));
      }
    }
  }, [widgetsById, layoutLoaded]);

  React.useEffect(() => {
    if (initialized && layoutLoaded) {
      window.sessionStorage.setItem(PersistStorageKey.initialized, 'true');
      const previousTheme = window.sessionStorage.getItem(SessionStorageKey.theme);
      if (previousTheme && previousTheme !== theme) {
        window.sessionStorage.setItem(SessionStorageKey.theme, theme);
        // we need to perform reload for one specific case -
        // the user logged out and another user with different UI theme logs in
        window.location.reload();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [layoutLoaded, initialized]);

  const serverLayoutSave = Hooks.useThrottleLeading(saveServer, FLUSH_TIMEOUT);

  React.useEffect(() => {
    serverLayoutSave(layoutLoaded, settingTransformed, widgetsTransformed, workspacesTransformed, session.getSession());
  }, [layoutLoaded, serverLayoutSave, session, settingTransformed, widgetsTransformed, workspacesTransformed]);

  React.useEffect(() => {
    const handle = () => {
      saveServer(layoutLoaded, settingTransformed, widgetsTransformed, workspacesTransformed, session.getSession());
    };
    window.addEventListener('beforeunload', handle);
    return () => window.removeEventListener('beforeunload', handle);
  }, [layoutLoaded, session, settingTransformed, widgetsTransformed, workspacesTransformed]);

  return <>{props.children}</>;
};

export default connect<ReduxState, null, React.PropsWithChildren<any>, RootState>(mapStateToProps)(LayoutsGate);
