import { isPopout } from '@benzinga/pro-ui';
import { RootState, Thunk } from '../redux/types';

////////////////////////////////////////////
///////      importing entities      ///////
////////////////////////////////////////////

import { WidgetId, WidgetType, Widget } from '../entities/widgetEntity';
import { WorkspaceId } from '../components/dashboard/workspaceEntity';

//////////////////////////////////////////////
///////      importing parameters      ///////
//////////////////////////////////////////////

/////////////////////////////////////////////
///////      importing selectors      ///////
/////////////////////////////////////////////

import { selectWidgetById } from '../selectors/widgetSelectors';
import { selectActiveWorkspaceId } from '../components/dashboard/workspaceSelectors';

///////////////////////////////////////////
///////      importing actions      ///////
///////////////////////////////////////////

import { setMobileWidget, setStreamVisibility } from './settingsActions';
import { addWorkspace } from '../components/dashboard/workspaceActions';
import { generateWidgetId } from '../utils/entities';
import { Manifest } from '../manifest';
import { LoggingManager, PersistStorageKey } from '@benzinga/session';
import { Loader, WidgetConfig, WidgetManifest, WidgetToolsManager } from '@benzinga/widget-tools';
import { UnpackedArray } from '@benzinga/utils';
import { TrackingManager } from '@benzinga/tracking-manager';
import { selectSession } from '../selectors/sessionSelectors';
import { LayoutManager } from '@benzinga/layout-manager';

//////////////////////////////////////////
///////      define constants      ///////
//////////////////////////////////////////

export const ADD_WIDGET = 'ADD_WIDGET';
export const REMOVE_WIDGET = 'REMOVE_WIDGET';

export interface BaseAddWidgetAction<
  W extends Widget<TYPE, DATA>,
  TYPE extends string = W extends Widget<infer T, object> ? T : never,
  DATA extends object = W extends Widget<string, infer D> ? D : never,
  VERSION extends number = number,
> {
  payload: {
    parameters?: Partial<DATA>;
    widgetId: WidgetId;
    widgetType: TYPE;
    widgetVersion: VERSION;
    workspaceId?: WorkspaceId;
  };
  state: RootState;
  type: typeof ADD_WIDGET;
}

///////////////////////////////////////////////////
///////      export add widget actions      ///////
///////////////////////////////////////////////////

export type AddWidgetAction = BaseAddWidgetAction<Widget>;

export const addWidget =
  (
    widgetType: WidgetType,
    widgetConfig?: WidgetConfig,
    workspace: WorkspaceId | 'current' | 'new' | 'none' = 'current',
  ): Thunk<Promise<string | null>> =>
  async (dispatch, getState): Promise<string | null> => {
    const state = getState();
    let workspaceId: string | undefined;

    switch (workspace) {
      case 'current':
        workspaceId = selectActiveWorkspaceId(state);
        if (
          !workspaceId ||
          workspaceId === 'home-page' ||
          workspaceId === 'welcome-page' ||
          workspaceId === 'morning-report'
        )
          return dispatch(addWidget(widgetType, widgetConfig, 'new'));
        break;
      case 'new':
        workspaceId = addWorkspace()(dispatch, getState).payload.workspaceId;
        if (!workspaceId) return dispatch(addWidget(widgetType, widgetConfig, 'none'));
        break;
      case 'none':
        workspaceId = undefined;
        break;
      default:
        workspaceId = workspace;
    }

    const widgetId = generateWidgetId();

    const run = async () => {
      const getParameters = async () => {
        const manifest = Manifest.find(
          manifest => manifest.id === (widgetType as UnpackedArray<typeof Manifest>['id']),
        ) as WidgetManifest<string, Loader, Loader[]> | undefined;
        if (manifest) {
          if (manifest.initWidgetParameters) {
            try {
              const params = manifest.initWidgetParameters(
                widgetConfig
                  ? { version: widgetConfig.widgetVersion, widgetParameters: widgetConfig.parameters ?? {} }
                  : undefined,
                getState().settings.widgetGlobalSettings[widgetType] ?? {},
                selectSession(getState()),
              );
              if (params instanceof Promise) {
                return await params;
              } else {
                return params;
              }
            } catch (e) {
              getState()
                .session?.getManager(LoggingManager)
                .log('error', { category: manifest.id, message: `Error initWidgetParameters cb for ${manifest.id}` }, [
                  'console',
                ]);
              return { ...manifest.defaultWidgetParameters, ...widgetConfig?.parameters };
            }
          } else {
            return { ...manifest.defaultWidgetParameters, ...widgetConfig?.parameters };
          }
        } else {
          return widgetConfig?.parameters;
        }
      };

      const action: AddWidgetAction = {
        payload: {
          parameters: {
            ...(await getParameters()),
            isPopout: isPopout as boolean,
          } as any,
          widgetId,
          widgetType: widgetType as any,
          widgetVersion:
            widgetConfig?.widgetVersion ?? Manifest.find(manifest => manifest.id === widgetType)?.version ?? 1,
          workspaceId,
        },
        state,
        type: ADD_WIDGET,
      };

      const layoutManager = selectSession(getState()).getManager(LayoutManager);
      const cachedLayout = layoutManager.getCachedLayout();
      //sending new widget to layout manager
      layoutManager.saveLayout({
        replace: {
          data: action.payload,
          uuid: window.sessionStorage.getItem(PersistStorageKey.activeLayoutId) ?? cachedLayout?.uuid ?? 'key',
        },
      });
      dispatch(action);

      try {
        selectSession(getState()).getManager(WidgetToolsManager).updateManager({
          moduleId: widgetType,
          type: 'widgetAdded',
          widgetId,
        });
      } catch (e) {
        console.error(`Error onWidgetAddedList cb for ${widgetType}`, e);
      }

      selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('add_to_workspace', {
        widget_id: widgetId,
        widget_type: widgetType,
        workspace_id: workspaceId,
      });
    };
    await run();
    return widgetId;
  };

export interface RemoveWidgetAction {
  payload: {
    widgetId: WidgetId;
    workspaceId: WorkspaceId;
  };
  type: typeof REMOVE_WIDGET;
}

export const removeWidget: (widgetId: WidgetId) => Thunk<void> =
  (widgetId: WidgetId): Thunk<void> =>
  (dispatch, getState) => {
    if (isPopout) {
      window.close();
    }
    const layoutManager = selectSession(getState()).getManager(LayoutManager);
    const cachedLayout = layoutManager.getCachedLayout();

    const state = getState();
    const widget = selectWidgetById(state, { widgetId });

    if (widget && widget.widgetId === state.settings.globalChatWidgetId) {
      return dispatch(setStreamVisibility(false));
    } else if (widget && widget?.widgetId === state.settings.mobileWidgetId) {
      dispatch(setMobileWidget(null));
    }

    const workspaceId = selectActiveWorkspaceId(state);
    // TODO pass removeWidget a callback that the widget itself can pass through to do this stuff
    // that action would be applied with dispatch here like
    // if (cb) dispatch(cb)

    if (workspaceId !== 'home-page') {
      const removeWidgetAction: RemoveWidgetAction = {
        payload: {
          widgetId,
          workspaceId,
        },
        type: REMOVE_WIDGET,
      };

      //sending removed widget to layout manager
      layoutManager.saveLayout({
        remove: {
          data: removeWidgetAction.payload,
          uuid: window.sessionStorage.getItem(PersistStorageKey.activeLayoutId) ?? cachedLayout?.uuid ?? 'key',
        },
      });

      dispatch(removeWidgetAction);

      try {
        getState()
          .session?.getManager(WidgetToolsManager)
          .updateManager({
            moduleId: widget?.type ?? '',
            type: 'widgetRemoved',
            widgetId,
          });
      } catch (e) {
        console.error(`Error onWidgetRemovedList cb for ${widget?.type}`, e);
      }

      selectSession(getState()).getManager(TrackingManager).trackLayoutEvent('delete_from_workspace', {
        layout_id: 'stock',
        widget_id: widgetId,
        workspace_id: workspaceId,
      });
    }
  };

export const SET_FLIGHT_MODE = 'SET_FLIGHT_MODE';
export interface SetFlightModeAction {
  payload: {
    widgetId: WidgetId;
    flightMode: boolean;
  };
  type: typeof SET_FLIGHT_MODE;
}

export const setFlightMode = (widgetId: WidgetId, flightMode: boolean): SetFlightModeAction => ({
  payload: {
    flightMode,
    widgetId,
  },
  type: SET_FLIGHT_MODE,
});
///////////////////////////////////////////////
///////      export widget actions      ///////
///////////////////////////////////////////////

export type WidgetActions = AddWidgetAction | RemoveWidgetAction | SetFlightModeAction;
